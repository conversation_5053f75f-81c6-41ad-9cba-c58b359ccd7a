const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"certificate.verify":{"uri":"certificate\/verify","methods":["GET","HEAD"]},"about":{"uri":"about","methods":["GET","HEAD"]},"events":{"uri":"events","methods":["GET","HEAD"]},"previous-committee":{"uri":"previous-committee","methods":["GET","HEAD"]},"previous-committee.show":{"uri":"previous-committee\/{committee}","methods":["GET","HEAD"],"parameters":["committee"]},"previous-committee.data":{"uri":"previous-committee\/data\/{committee}","methods":["GET","HEAD"],"parameters":["committee"]},"api.committees":{"uri":"api\/committees","methods":["GET","HEAD"]},"api.committee":{"uri":"api\/committees\/{committee}","methods":["GET","HEAD"],"parameters":["committee"]},"find-member":{"uri":"find-member","methods":["GET","HEAD"]},"museum.index":{"uri":"museum","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"volunteer-application.create":{"uri":"volunteer-application\/create","methods":["GET","HEAD"]},"volunteer-application.store":{"uri":"volunteer-application","methods":["POST"]},"executive-application.create":{"uri":"executive-application\/create","methods":["GET","HEAD"]},"executive-application.store":{"uri":"executive-application","methods":["POST"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]},"admin.login":{"uri":"admin\/login","methods":["GET","HEAD"]},"admin.":{"uri":"admin\/login","methods":["POST"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.export":{"uri":"admin\/users\/export","methods":["GET","HEAD"]},"admin.users.template":{"uri":"admin\/users\/template","methods":["GET","HEAD"]},"admin.users.import":{"uri":"admin\/users\/import","methods":["POST"]},"admin.users.import.preview":{"uri":"admin\/users\/import\/preview","methods":["POST"]},"admin.users.import.validate-row":{"uri":"admin\/users\/import\/validate-row","methods":["POST"]},"admin.users.import.batch":{"uri":"admin\/users\/import\/batch","methods":["POST"]},"admin.users.import.status":{"uri":"admin\/users\/import\/status","methods":["GET","HEAD"]},"admin.users.import.clear-session":{"uri":"admin\/users\/import\/clear-session","methods":["POST"]},"admin.users.import.validation-metadata":{"uri":"admin\/users\/import\/validation-metadata","methods":["GET","HEAD"]},"admin.users.import-wizard":{"uri":"admin\/users\/import-wizard","methods":["GET","HEAD"]},"admin.users.all":{"uri":"admin\/users\/all","methods":["GET","HEAD"]},"admin.users.pending":{"uri":"admin\/users\/pending","methods":["GET","HEAD"]},"admin.users.approve":{"uri":"admin\/users\/{id}\/approve","methods":["POST"],"parameters":["id"]},"admin.users.reject":{"uri":"admin\/users\/{id}\/reject","methods":["DELETE"],"parameters":["id"]},"admin.users.bulk-approve":{"uri":"admin\/users\/bulk-approve","methods":["POST"]},"admin.users.bulk-reject":{"uri":"admin\/users\/bulk-reject","methods":["POST"]},"admin.users.show":{"uri":"admin\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.edit":{"uri":"admin\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"admin.applications.volunteer.index":{"uri":"admin\/applications\/volunteer","methods":["GET","HEAD"]},"admin.applications.volunteer.show":{"uri":"admin\/applications\/volunteer\/{application}","methods":["GET","HEAD"],"parameters":["application"],"bindings":{"application":"id"}},"admin.applications.volunteer.update":{"uri":"admin\/applications\/volunteer\/{application}","methods":["PATCH"],"parameters":["application"],"bindings":{"application":"id"}},"admin.applications.volunteer.destroy":{"uri":"admin\/applications\/volunteer\/{application}","methods":["DELETE"],"parameters":["application"],"bindings":{"application":"id"}},"admin.applications.executive.index":{"uri":"admin\/applications\/executive","methods":["GET","HEAD"]},"admin.applications.executive.show":{"uri":"admin\/applications\/executive\/{application}","methods":["GET","HEAD"],"parameters":["application"],"bindings":{"application":"id"}},"admin.applications.executive.update":{"uri":"admin\/applications\/executive\/{application}","methods":["PATCH"],"parameters":["application"],"bindings":{"application":"id"}},"admin.applications.executive.destroy":{"uri":"admin\/applications\/executive\/{application}","methods":["DELETE"],"parameters":["application"],"bindings":{"application":"id"}},"admin.user-role-management.index":{"uri":"admin\/user-role-management","methods":["GET","HEAD"]},"admin.user-role-management.update-role":{"uri":"admin\/user-role-management\/{user}\/update-role","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"admin.user-role-management.history":{"uri":"admin\/user-role-management\/{user}\/history","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.admin.designations.index":{"uri":"admin\/designations","methods":["GET","HEAD"]},"admin.admin.designations.create":{"uri":"admin\/designations\/create","methods":["GET","HEAD"]},"admin.admin.designations.store":{"uri":"admin\/designations","methods":["POST"]},"admin.admin.designations.show":{"uri":"admin\/designations\/{designation}","methods":["GET","HEAD"],"parameters":["designation"],"bindings":{"designation":"id"}},"admin.admin.designations.edit":{"uri":"admin\/designations\/{designation}\/edit","methods":["GET","HEAD"],"parameters":["designation"],"bindings":{"designation":"id"}},"admin.admin.designations.update":{"uri":"admin\/designations\/{designation}","methods":["PUT","PATCH"],"parameters":["designation"],"bindings":{"designation":"id"}},"admin.admin.designations.destroy":{"uri":"admin\/designations\/{designation}","methods":["DELETE"],"parameters":["designation"],"bindings":{"designation":"id"}},"admin.notifications.check":{"uri":"admin\/notifications\/check","methods":["GET","HEAD"]},"admin.notifications.mark-seen":{"uri":"admin\/notifications\/mark-seen","methods":["POST"]},"admin.certificate.types.index":{"uri":"admin\/certificate\/types","methods":["GET","HEAD"]},"admin.certificate.types.create":{"uri":"admin\/certificate\/types\/create","methods":["GET","HEAD"]},"admin.certificate.types.store":{"uri":"admin\/certificate\/types","methods":["POST"]},"admin.certificate.types.edit":{"uri":"admin\/certificate\/types\/edit\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"admin.certificate.types.delete":{"uri":"admin\/certificate\/types\/delete\/{id}","methods":["DELETE"],"parameters":["id"]},"admin.certificate.templates.index":{"uri":"admin\/certificate\/templates","methods":["GET","HEAD"]},"admin.certificate.templates.create":{"uri":"admin\/certificate\/templates\/create","methods":["GET","HEAD"]},"admin.certificate.templates.store":{"uri":"admin\/certificate\/templates","methods":["POST"]},"admin.certificate.templates.edit":{"uri":"admin\/certificate\/templates\/{template}\/edit","methods":["GET","HEAD"],"parameters":["template"]},"admin.certificate.templates.design":{"uri":"admin\/certificate\/templates\/{template}\/design","methods":["GET","HEAD"],"parameters":["template"]},"admin.certificate.templates.design.update":{"uri":"admin\/certificate\/templates\/design\/update","methods":["POST"]},"admin.certificate.templates.design.reset":{"uri":"admin\/certificate\/templates\/{template}\/design\/reset","methods":["POST"],"parameters":["template"]},"admin.certificate.templates.destroy":{"uri":"admin\/certificate\/templates\/{template}","methods":["DELETE"],"parameters":["template"]},"admin.certificate.templates.type":{"uri":"admin\/certificate\/templates\/type","methods":["POST"]},"admin.certificate.templates.preview":{"uri":"admin\/certificate\/templates\/preview","methods":["POST"]},"admin.certificate.settings":{"uri":"admin\/certificate\/settings","methods":["GET","HEAD"]},"admin.certificate.settings-store":{"uri":"admin\/certificate\/settings","methods":["POST"]},"admin.certificate.generate":{"uri":"admin\/certificate\/generate","methods":["GET","HEAD"]},"admin.certificate.generate.users":{"uri":"admin\/certificate\/generate\/users","methods":["GET","HEAD"]},"admin.certificate.generate.certificates":{"uri":"admin\/certificate\/generate\/certificates","methods":["POST"]},"admin.certificate.generate.save":{"uri":"admin\/certificate\/generate\/save","methods":["POST"]},"admin.certificate.records":{"uri":"admin\/certificate\/records","methods":["GET","HEAD"]},"admin.certificate.searchCertificates":{"uri":"admin\/certificate\/records","methods":["POST"]},"admin.certificate.record_show":{"uri":"admin\/certificate\/record\/{record_id}","methods":["GET","HEAD"],"parameters":["record_id"]},"admin.certificate.record_delete":{"uri":"admin\/certificate\/record-delete\/{record_id}","methods":["GET","HEAD"],"parameters":["record_id"]},"admin.certificate.record_delete_multiple":{"uri":"admin\/certificate\/record-delete-multiple","methods":["GET","HEAD"]},"admin.certificate.record_download":{"uri":"admin\/certificate\/record-download","methods":["GET","HEAD"]},"admin.certificate.record_print":{"uri":"admin\/certificate\/record-print\/{record_id}","methods":["GET","HEAD"],"parameters":["record_id"]},"admin.logout":{"uri":"admin\/logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
