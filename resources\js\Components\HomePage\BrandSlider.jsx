/* eslint-disable */
import React, { useEffect, useRef, useState } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const brands = [
  {
    src: '/img/brands/gp.png',
    name: 'Grameenphone',
    glow: 'from-green-400 to-emerald-600',
    description: 'Leading telecom provider'
  },
  {
    src: '/img/brands/ucb.png',
    name: 'UCB Bank',
    glow: 'from-blue-400 to-cyan-600',
    description: 'Trusted banking partner'
  },
  {
    src: '/img/brands/ultragear.png',
    name: 'UltraGear',
    glow: 'from-red-400 to-pink-600',
    description: 'Gaming excellence'
  },
  {
    src: '/img/brands/undp.jpg',
    name: 'UNDP',
    glow: 'from-purple-400 to-indigo-600',
    description: 'Development partner'
  },
]

// Create infinite scroll by duplicating brands
const infiniteBrands = [...brands, ...brands, ...brands]

export default function BrandSlider() {
  const sectionRef = useRef(null)
  const trackRef = useRef(null)
  const particlesRef = useRef(null)
  const [activeIndex, setActiveIndex] = useState(0)
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const section = sectionRef.current
    const track = trackRef.current
    if (!section || !track) return

    const ctx = gsap.context(() => {
      const items = gsap.utils.toArray('.brand-item')
      const particleElements = gsap.utils.toArray('.particle')

      // Create floating particles animation
      particleElements.forEach((particle, i) => {
        gsap.set(particle, {
          x: gsap.utils.random(0, window.innerWidth),
          y: gsap.utils.random(0, window.innerHeight),
          scale: gsap.utils.random(0.1, 0.4),
          opacity: gsap.utils.random(0.1, 0.5),
        })

        gsap.to(particle, {
          x: `+=${gsap.utils.random(-300, 300)}`,
          y: `+=${gsap.utils.random(-150, 150)}`,
          rotation: gsap.utils.random(0, 360),
          duration: gsap.utils.random(10, 20),
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut',
          delay: i * 0.3,
        })
      })

      // Insane floating animation for brand items
      items.forEach((el, i) => {
        // Multi-layered floating with 3D transforms
        gsap.to(el, {
          y: () => gsap.utils.random(-20, 20),
          x: () => gsap.utils.random(-8, 8),
          rotateX: () => gsap.utils.random(-10, 10),
          rotateY: () => gsap.utils.random(-10, 10),
          rotateZ: () => gsap.utils.random(-5, 5),
          duration: () => gsap.utils.random(4, 8),
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut',
          delay: i * 0.2,
        })

        // Pulsing glow effect
        const glow = el.querySelector('.brand-glow')
        if (glow) {
          gsap.to(glow, {
            scale: gsap.utils.random(1.2, 1.6),
            opacity: gsap.utils.random(0.4, 0.8),
            duration: gsap.utils.random(3, 6),
            repeat: -1,
            yoyo: true,
            ease: 'power2.inOut',
            delay: i * 0.15,
          })
        }
      })

      const enableHorizontal = () => {
        const total = track.scrollWidth - window.innerWidth
        gsap.set(track, { x: 0 })

        // Enhanced horizontal scroll timeline
        const tl = gsap.timeline({
          defaults: { ease: 'none' },
          scrollTrigger: {
            trigger: section,
            start: 'top top',
            end: () => `+=${total * 1.5}`, // Extended scroll distance
            scrub: 0.8, // Smoother scrubbing
            pin: true,
            anticipatePin: 1,
            invalidateOnRefresh: true,
            onUpdate: (self) => {
              // Update active index based on scroll progress
              const progress = self.progress
              const newIndex = Math.floor(progress * (brands.length - 1))
              setActiveIndex(newIndex)
            },
          },
        })

        tl.to(track, {
          x: () => -1 * (track.scrollWidth - window.innerWidth),
          ease: 'power1.inOut',
        })

        // Insane spotlight and transformation effects
        items.forEach((el, i) => {
          const img = el.querySelector('img')
          const glow = el.querySelector('.brand-glow')

          // Multi-stage transformation as items approach center
          gsap.fromTo(
            el,
            {
              opacity: 0.3,
              scale: 0.8,
              rotateY: -60,
              z: -300,
              filter: 'blur(8px) brightness(0.4)',
            },
            {
              opacity: 1,
              scale: 1.15,
              rotateY: 0,
              z: 0,
              filter: 'blur(0px) brightness(1.2)',
              duration: 0.8,
              ease: 'back.out(1.7)',
              scrollTrigger: {
                trigger: el,
                start: 'left 85%',
                end: 'left 15%',
                horizontal: true,
                containerAnimation: tl,
                toggleActions: 'play reverse play reverse',
                onEnter: () => {
                  // Explosive entrance effect
                  gsap.to(el, {
                    scale: 1.3,
                    duration: 0.15,
                    yoyo: true,
                    repeat: 1,
                    ease: 'power2.out',
                  })
                },
              },
            }
          )

          // Image-specific animations
          if (img) {
            gsap.fromTo(
              img,
              {
                scale: 0.6,
                rotation: -270,
                filter: 'blur(15px) saturate(0.3)',
              },
              {
                scale: 1,
                rotation: 0,
                filter: 'blur(0px) saturate(1.2)',
                duration: 1,
                ease: 'elastic.out(1, 0.6)',
                scrollTrigger: {
                  trigger: el,
                  start: 'left 75%',
                  end: 'left 25%',
                  horizontal: true,
                  containerAnimation: tl,
                  toggleActions: 'play reverse play reverse',
                },
              }
            )
          }

          // Glow intensification
          if (glow) {
            gsap.fromTo(
              glow,
              {
                scale: 0.5,
                opacity: 0,
              },
              {
                scale: 2,
                opacity: 0.9,
                duration: 0.6,
                ease: 'power2.out',
                scrollTrigger: {
                  trigger: el,
                  start: 'left center',
                  horizontal: true,
                  containerAnimation: tl,
                  toggleActions: 'play reverse play reverse',
                },
              }
            )
          }
        })
      }

      const enableMarqueeMobile = () => {
        // Enhanced mobile marquee with multiple layers
        const distance = track.scrollWidth / 3
        gsap.set(track, { x: 0 })

        // Main marquee
        gsap.to(track, {
          x: -distance,
          duration: 25,
          ease: 'none',
          repeat: -1,
        })

        // Individual item animations for mobile
        items.forEach((el, i) => {
          gsap.to(el, {
            rotateY: 360,
            duration: 12,
            repeat: -1,
            ease: 'none',
            delay: i * 0.8,
          })

          // Mobile glow effects
          const glow = el.querySelector('.brand-glow')
          if (glow) {
            gsap.to(glow, {
              opacity: 0.6,
              scale: 1.3,
              duration: 3,
              repeat: -1,
              yoyo: true,
              ease: 'sine.inOut',
              delay: i * 0.5,
            })
          }
        })
      }

      const mm = gsap.matchMedia()
      mm.add('(min-width: 768px)', enableHorizontal)
      mm.add('(max-width: 767px)', enableMarqueeMobile)
    }, section)

    return () => ctx.revert()
  }, [])

  // Mouse tracking for interactive effects
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePos({ x: e.clientX, y: e.clientY })
    }
    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  return (
    <section
      ref={sectionRef}
      className="relative w-screen bg-gradient-to-br from-[#0a0b14] via-[#1a1b2e] to-[#16213e] py-20 md:py-32 select-none overflow-hidden"
      aria-label="Our Partners"
      style={{
        background: `
          radial-gradient(circle at ${mousePos.x}px ${mousePos.y}px, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
          linear-gradient(135deg, #0a0b14 0%, #1a1b2e 50%, #16213e 100%)
        `
      }}
    >
      {/* Floating Particles */}
      <div ref={particlesRef} className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 60 }).map((_, i) => (
          <div
            key={i}
            className="particle absolute w-1 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full"
            style={{
              boxShadow: '0 0 8px currentColor',
            }}
          />
        ))}
      </div>

      {/* Insane Background Effects */}
      <div className="pointer-events-none absolute inset-0">
        {/* Multiple layered glows */}
        <div className="absolute -top-40 left-1/4 h-[70rem] w-[70rem] -translate-x-1/2 rounded-full bg-gradient-to-br from-cyan-400/25 via-blue-500/20 to-purple-500/15 blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 right-1/4 h-[60rem] w-[60rem] translate-x-1/2 rounded-full bg-gradient-to-tl from-pink-400/20 via-purple-500/15 to-indigo-500/15 blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }} />
        <div className="absolute top-1/2 left-1/2 h-[50rem] w-[50rem] -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-emerald-400/15 via-teal-500/12 to-cyan-500/15 blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />

        {/* Animated grid with glow */}
        <div className="absolute inset-0 bg-[linear-gradient(0deg,transparent_24px,rgba(59,130,246,0.08)_25px),linear-gradient(90deg,transparent_24px,rgba(59,130,246,0.08)_25px)] bg-[size:26px_26px] animate-pulse" />

        {/* Scanning lines */}
        <div className="absolute inset-0 animate-pulse" style={{
          background: 'linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.15) 50%, transparent 100%)',
          animation: 'scan 6s ease-in-out infinite'
        }} />
      </div>

      <div className="relative mx-auto max-w-7xl px-6">
        {/* Epic Header */}
        <div className="mb-10 md:mb-16 flex items-end justify-between">
          <div className="relative">
            <div className="absolute -inset-6 bg-gradient-to-r from-cyan-400/25 to-purple-400/25 blur-2xl rounded-lg" />
            <div className="relative">
              <p className="text-sm uppercase tracking-[0.4em] text-cyan-300/90 font-bold animate-pulse flex items-center gap-2">
                <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                ⚡ TRUSTED PARTNERSHIPS
              </p>
              <h2 className="mt-3 text-3xl md:text-7xl font-black bg-clip-text text-transparent bg-gradient-to-r from-white via-cyan-200 to-purple-200 leading-tight">
                Powered by
                <span className="block bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse">
                  Innovation Leaders
                </span>
              </h2>
            </div>
          </div>
          <div className="hidden md:block text-sm text-white/80 max-w-sm relative">
            <div className="absolute -inset-3 bg-gradient-to-r from-purple-400/15 to-cyan-400/15 blur-xl rounded-lg" />
            <div className="relative bg-black/30 backdrop-blur-xl rounded-xl p-5 border border-white/20">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className="text-xs text-green-400 font-bold">LIVE SHOWCASE</span>
              </div>
              <p className="leading-relaxed">Immersive horizontal journey powered by GSAP ScrollTrigger. Scroll to explore our ecosystem partners and their innovations.</p>
            </div>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="mb-12 flex justify-center">
          <div className="flex gap-3">
            {brands.map((_, i) => (
              <div
                key={i}
                className={`h-1.5 w-10 rounded-full transition-all duration-700 ${
                  i === activeIndex
                    ? 'bg-gradient-to-r from-cyan-400 to-purple-400 shadow-lg shadow-cyan-400/50 scale-110'
                    : 'bg-white/30 hover:bg-white/50'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Main Brand Showcase */}
        <div className="relative overflow-visible">
          <div ref={trackRef} className="flex w-max items-center gap-16 md:gap-24 will-change-transform">
            {infiniteBrands.map((brand, i) => (
              <div
                key={i}
                className="brand-item group relative flex h-36 w-[16rem] md:h-44 md:w-[22rem] items-center justify-center rounded-3xl border border-white/20 bg-black/40 backdrop-blur-xl hover:border-cyan-400/60 transition-all duration-700 [transform-style:preserve-3d] cursor-pointer"
                style={{
                  background: `
                    linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.03) 100%),
                    radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.15) 0%, transparent 70%)
                  `,
                  boxShadow: `
                    0 12px 40px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.15),
                    0 0 0 1px rgba(255, 255, 255, 0.08)
                  `
                }}
              >
                {/* Animated Glow */}
                <div className={`brand-glow absolute -inset-6 bg-gradient-to-r ${brand.glow} opacity-0 group-hover:opacity-40 blur-2xl rounded-3xl transition-all duration-700`} />

                {/* Brand Image Container with consistent sizing */}
                <div className="relative w-20 h-20 md:w-24 md:h-24 flex items-center justify-center">
                  <img
                    src={brand.src}
                    alt={brand.name}
                    className="max-w-full max-h-full object-contain opacity-75 group-hover:opacity-100 transition-all duration-700 relative z-10 filter drop-shadow-2xl"
                    style={{
                      // Ensure all images are exactly the same display size
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                    }}
                    loading="lazy"
                  />
                </div>

                {/* Brand Info */}
                <div className="absolute -bottom-12 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-700 text-center">
                  <span className="text-sm font-bold text-white/90 bg-black/60 backdrop-blur-sm px-4 py-2 rounded-full border border-white/30 block mb-1">
                    {brand.name}
                  </span>
                  <span className="text-xs text-white/60 bg-black/40 backdrop-blur-sm px-3 py-1 rounded-full border border-white/20">
                    {brand.description}
                  </span>
                </div>

                {/* Interactive Hover Effect */}
                <div className="pointer-events-none absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 bg-[radial-gradient(600px_200px_at_var(--x,50%)_var(--y,50%),rgba(59,130,246,0.2),transparent_60%)]" />

                {/* Corner Accents */}
                <div className="absolute top-3 right-3 w-4 h-4 border-t-2 border-r-2 border-cyan-400/60 opacity-0 group-hover:opacity-100 transition-all duration-700" />
                <div className="absolute bottom-3 left-3 w-4 h-4 border-b-2 border-l-2 border-purple-400/60 opacity-0 group-hover:opacity-100 transition-all duration-700" />

                {/* Pulse Ring */}
                <div className="absolute inset-0 rounded-3xl border-2 border-cyan-400/30 opacity-0 group-hover:opacity-100 group-hover:scale-110 transition-all duration-700" />
              </div>
            ))}
          </div>
        </div>

        {/* Scroll Hint */}
        <div className="mt-16 flex justify-center">
          <div className="flex items-center gap-4 text-white/60 text-sm animate-bounce">
            <div className="w-7 h-12 border-2 border-white/40 rounded-full relative">
              <div className="w-1.5 h-4 bg-gradient-to-b from-cyan-400 to-purple-400 rounded-full absolute top-2 left-1/2 -translate-x-1/2 animate-pulse" />
            </div>
            <span className="font-medium">Scroll to explore partnerships</span>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes scan {
          0%, 100% { transform: translateX(-100%); }
          50% { transform: translateX(100%); }
        }
      `}</style>
    </section>
  )
}
