/* eslint-disable */
import React, { useEffect, useRef } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const brands = [
  '/img/brands/brand-01.svg',
  '/img/brands/brand-02.svg',
  '/img/brands/brand-03.svg', 
  '/img/brands/brand-04.svg',
  '/img/brands/brand-05.svg',

]

export default function BrandSlider() {
  const sectionRef = useRef(null)
  const trackRef = useRef(null)

  useEffect(() => {
    const section = sectionRef.current
    const track = trackRef.current
    if (!section || !track) return

    const ctx = gsap.context(() => {
      const items = gsap.utils.toArray('.brand-item')

      // subtle float for life
      items.forEach((el, i) => {
        gsap.to(el, {
          y: () => gsap.utils.random(-8, 8),
          rotate: () => gsap.utils.random(-1, 1),
          duration: () => gsap.utils.random(2, 4),
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut',
          delay: i * 0.1,
        })
      })

      const enableHorizontal = () => {
        const total = track.scrollWidth - window.innerWidth
        // Reset position on refresh
        gsap.set(track, { x: 0 })

        // Pin the whole section and scrub horizontally
        const tl = gsap.timeline({
          defaults: { ease: 'none' },
          scrollTrigger: {
            trigger: section,
            start: 'top top',
            end: () => `+=${total}`,
            scrub: 1,
            pin: true,
            anticipatePin: 1,
            invalidateOnRefresh: true,
          },
        })

        tl.to(track, {
          x: () => -1 * (track.scrollWidth - window.innerWidth),
        })

        // punch-up spotlight effect as items approach center
        items.forEach((el) => {
          gsap.fromTo(
            el,
            { opacity: 0.35, scale: 0.95 },
            {
              opacity: 1,
              scale: 1,
              duration: 0.4,
              ease: 'power1.out',
              scrollTrigger: {
                trigger: el,
                start: 'left center',
                horizontal: true,
                containerAnimation: tl, // sync with the horizontal timeline
                toggleActions: 'play reverse play reverse',
              },
            }
          )
        })
      }

      const enableMarqueeMobile = () => {
        // simple marquee on small screens
        const distance = track.scrollWidth / 2
        gsap.set(track, { x: 0 })
        gsap.to(track, {
          x: -distance,
          duration: 15,
          ease: 'none',
          repeat: -1,
        })
      }

      const mm = gsap.matchMedia()
      mm.add('(min-width: 768px)', enableHorizontal)
      mm.add('(max-width: 767px)', enableMarqueeMobile)
    }, section)

    return () => ctx.revert()
  }, [])

  return (
    <section
      ref={sectionRef}
      className="relative w-screen bg-[#07080d] py-20 md:py-32 select-none"
      aria-label="We work with"
    >
      {/* background glow + grid */}
      <div className="pointer-events-none absolute inset-0 [mask-image:radial-gradient(ellipse_at_center,black,transparent_70%)]">
        <div className="absolute -top-40 left-1/2 h-[50rem] w-[50rem] -translate-x-1/2 rounded-full bg-gradient-to-br from-cyan-400/15 via-indigo-500/10 to-fuchsia-500/10 blur-3xl" />
        <div className="absolute inset-0 bg-[linear-gradient(0deg,transparent_24px,rgba(255,255,255,0.03)_25px),linear-gradient(90deg,transparent_24px,rgba(255,255,255,0.03)_25px)] bg-[size:26px_26px]" />
      </div>

      <div className="relative mx-auto max-w-7xl px-6">
        <div className="mb-10 md:mb-16 flex items-end justify-between">
          <div>
            <p className="text-sm uppercase tracking-[0.2em] text-cyan-300/70">We work with</p>
            <h2 className="mt-2 text-3xl md:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-white via-cyan-200 to-fuchsia-200">
              Trusted by forward‑thinking teams
            </h2>
          </div>
          <div className="hidden md:block text-sm text-white/60 max-w-sm">
            Pinned horizontal showcase powered by GSAP ScrollTrigger. Scroll to explore our ecosystem partners.
          </div>
        </div>

        <div className="relative overflow-visible">
          <div ref={trackRef} className="flex w-max items-center gap-8 md:gap-14 will-change-transform">
            {brands.map((src, i) => (
              <div
                key={i}
                className="brand-item group relative flex h-28 w-[12rem] md:h-28 md:w-[16rem] items-center justify-center rounded-2xl border border-white/10 bg-white/[0.02] px-6 backdrop-blur-sm hover:border-cyan-400/30 hover:bg-white/[0.04] transition-all duration-300 [transform-style:preserve-3d]"
              >
                <img
                  src={src}
                  alt={`Brand ${i + 1}`}
                  className="h-10 md:h-12 opacity-80 group-hover:opacity-100 transition-opacity"
                  loading="lazy"
                />
                <div className="pointer-events-none absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity bg-[radial-gradient(600px_200px_at_var(--x,50%)_-40%,rgba(34,211,238,0.15),transparent_60%)]" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
