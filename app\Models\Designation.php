<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Designation extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'level',
        'parent_id',
        'sort_order',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    // Parent-child relationships
    public function parent()
    {
        return $this->belongsTo(Designation::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Designation::class, 'parent_id')->orderBy('sort_order');
    }

    // Users with this designation
    public function users()
    {
        return $this->hasMany(User::class);
    }

    // Executive applications for this designation
    public function executiveApplications()
    {
        return $this->hasMany(ExecutiveApplication::class);
    }

    // Committee assignments for this designation
    public function committeeAssignments()
    {
        return $this->hasMany(CommitteeAssignment::class);
    }

    public function currentCommitteeAssignments()
    {
        return $this->hasMany(CommitteeAssignment::class)->where('status', 'current');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }
}
