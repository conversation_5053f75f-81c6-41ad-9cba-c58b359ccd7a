<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Cinematic Background Gradient -->
    <radialGradient id="cinematicBg" cx="50%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#0D1B2A;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#1B263B;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#415A77;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0D1B2A;stop-opacity:1" />
    </radialGradient>
    
    <!-- Electric Circuit Gradient -->
    <linearGradient id="electricGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00F5FF;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#FF1744;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00E676;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#FF6D00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00F5FF;stop-opacity:1" />
    </linearGradient>
    
    <!-- Hologram Flicker -->
    <filter id="hologramFlicker" x="-20%" y="-20%" width="140%" height="140%">
      <feColorMatrix type="hueRotate" values="0">
        <animate attributeName="values" values="0;360;0" dur="10s" repeatCount="indefinite"/>
      </feColorMatrix>
      <feGaussianBlur stdDeviation="1" result="blur"/>
      <feMerge>
        <feMergeNode in="blur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Neon Glow Intense -->
    <filter id="neonGlow" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- DNA Helix Pattern -->
    <pattern id="dnaPattern" x="0" y="0" width="80" height="160" patternUnits="userSpaceOnUse">
      <path d="M20 0 Q40 40 20 80 Q0 120 20 160" stroke="#00F5FF" stroke-width="2" fill="none" opacity="0.3"/>
      <path d="M60 0 Q40 40 60 80 Q80 120 60 160" stroke="#FF1744" stroke-width="2" fill="none" opacity="0.3"/>
      <circle cx="20" cy="20" r="3" fill="#00E676" opacity="0.6"/>
      <circle cx="60" cy="60" r="3" fill="#FF6D00" opacity="0.6"/>
      <circle cx="20" cy="100" r="3" fill="#00F5FF" opacity="0.6"/>
      <circle cx="60" cy="140" r="3" fill="#FF1744" opacity="0.6"/>
    </pattern>
    
    <!-- Quantum Particle -->
    <circle id="quantumParticle" r="3" fill="#00F5FF" filter="url(#neonGlow)">
      <animate attributeName="fill" values="#00F5FF;#FF1744;#00E676;#FF6D00;#00F5FF" dur="3s" repeatCount="indefinite"/>
    </circle>
  </defs>
  
  <!-- Cinematic Background -->
  <rect width="1920" height="1080" fill="url(#cinematicBg)"/>
  
  <!-- DNA Pattern Overlay -->
  <rect width="1920" height="1080" fill="url(#dnaPattern)" opacity="0.15">
    <animateTransform attributeName="transform" type="translate" values="0,0;0,-160;0,0" dur="20s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Central Quantum Computer Core -->
  <g transform="translate(960,540)">
    <!-- Main Quantum Chamber -->
    <rect x="-100" y="-100" width="200" height="200" rx="20" fill="none" stroke="url(#electricGrad)" stroke-width="3" opacity="0.8" filter="url(#hologramFlicker)">
      <animate attributeName="stroke-width" values="3;6;3" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Inner Quantum Processors -->
    <g>
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="15s" repeatCount="indefinite"/>
      <rect x="-60" y="-60" width="120" height="120" rx="10" fill="none" stroke="#00F5FF" stroke-width="2" opacity="0.7" filter="url(#neonGlow)"/>
      <rect x="-40" y="-40" width="80" height="80" rx="5" fill="none" stroke="#FF1744" stroke-width="2" opacity="0.7" filter="url(#neonGlow)"/>
      <rect x="-20" y="-20" width="40" height="40" rx="3" fill="none" stroke="#00E676" stroke-width="2" opacity="0.7" filter="url(#neonGlow)"/>
    </g>
    
    <!-- Quantum Data Streams -->
    <g opacity="0.8">
      <path d="M-80,-80 Q0,0 80,-80" stroke="#00F5FF" stroke-width="2" fill="none" filter="url(#neonGlow)">
        <animate attributeName="stroke-dasharray" values="0,300;150,150;300,0;150,150;0,300" dur="6s" repeatCount="indefinite"/>
      </path>
      <path d="M-80,80 Q0,0 80,80" stroke="#FF1744" stroke-width="2" fill="none" filter="url(#neonGlow)">
        <animate attributeName="stroke-dasharray" values="0,300;150,150;300,0;150,150;0,300" dur="6s" begin="2s" repeatCount="indefinite"/>
      </path>
      <path d="M-80,-80 Q0,0 -80,80" stroke="#00E676" stroke-width="2" fill="none" filter="url(#neonGlow)">
        <animate attributeName="stroke-dasharray" values="0,300;150,150;300,0;150,150;0,300" dur="6s" begin="4s" repeatCount="indefinite"/>
      </path>
      <path d="M80,-80 Q0,0 80,80" stroke="#FF6D00" stroke-width="2" fill="none" filter="url(#neonGlow)">
        <animate attributeName="stroke-dasharray" values="0,300;150,150;300,0;150,150;0,300" dur="6s" begin="1s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- Central Quantum Core -->
    <circle r="15" fill="#00F5FF" opacity="0.9" filter="url(#neonGlow)">
      <animate attributeName="r" values="10;20;10" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="fill" values="#00F5FF;#FF1744;#00E676;#FF6D00;#00F5FF" dur="5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Floating Quantum Particles -->
  <g>
    <!-- Top Left Quantum Field -->
    <g transform="translate(400,250)">
      <use href="#quantumParticle" transform="translate(0,0)">
        <animateTransform attributeName="transform" type="translate" values="0,0;50,-30;100,0;50,30;0,0" dur="12s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(80,40)">
        <animateTransform attributeName="transform" type="translate" values="80,40;30,10;-20,40;30,70;80,40" dur="15s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(-40,60)">
        <animateTransform attributeName="transform" type="translate" values="-40,60;10,90;60,60;10,30;-40,60" dur="18s" repeatCount="indefinite"/>
      </use>
    </g>
    
    <!-- Top Right Quantum Field -->
    <g transform="translate(1520,300)">
      <use href="#quantumParticle" transform="translate(0,0)">
        <animateTransform attributeName="transform" type="translate" values="0,0;-50,-20;-100,0;-50,20;0,0" dur="14s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(-60,-30)">
        <animateTransform attributeName="transform" type="translate" values="-60,-30;-10,-60;40,-30;-10,0;-60,-30" dur="16s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(30,50)">
        <animateTransform attributeName="transform" type="translate" values="30,50;-20,20;-70,50;-20,80;30,50" dur="20s" repeatCount="indefinite"/>
      </use>
    </g>
    
    <!-- Bottom Left Quantum Field -->
    <g transform="translate(300,780)">
      <use href="#quantumParticle" transform="translate(0,0)">
        <animateTransform attributeName="transform" type="translate" values="0,0;60,30;120,0;60,-30;0,0" dur="13s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(70,-40)">
        <animateTransform attributeName="transform" type="translate" values="70,-40;20,-10;-30,-40;20,-70;70,-40" dur="17s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(-50,30)">
        <animateTransform attributeName="transform" type="translate" values="-50,30;0,60;50,30;0,0;-50,30" dur="19s" repeatCount="indefinite"/>
      </use>
    </g>
    
    <!-- Bottom Right Quantum Field -->
    <g transform="translate(1600,820)">
      <use href="#quantumParticle" transform="translate(0,0)">
        <animateTransform attributeName="transform" type="translate" values="0,0;-40,-40;-80,0;-40,40;0,0" dur="11s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(-90,20)">
        <animateTransform attributeName="transform" type="translate" values="-90,20;-40,-10;10,20;-40,50;-90,20" dur="14s" repeatCount="indefinite"/>
      </use>
      <use href="#quantumParticle" transform="translate(20,-60)">
        <animateTransform attributeName="transform" type="translate" values="20,-60;70,-30;120,-60;70,-90;20,-60" dur="16s" repeatCount="indefinite"/>
      </use>
    </g>
  </g>
  
  <!-- Neural Network Connections -->
  <g opacity="0.4">
    <!-- Dynamic Connecting Lines -->
    <path d="M400,300 Q960,200 1520,350" stroke="url(#electricGrad)" stroke-width="1" fill="none" filter="url(#neonGlow)">
      <animate attributeName="stroke-dasharray" values="0,800;400,400;800,0;400,400;0,800" dur="8s" repeatCount="indefinite"/>
    </path>
    <path d="M350,800 Q960,600 1600,800" stroke="url(#electricGrad)" stroke-width="1" fill="none" filter="url(#neonGlow)">
      <animate attributeName="stroke-dasharray" values="0,800;400,400;800,0;400,400;0,800" dur="10s" begin="2s" repeatCount="indefinite"/>
    </path>
    <path d="M400,300 Q600,540 350,800" stroke="url(#electricGrad)" stroke-width="1" fill="none" filter="url(#neonGlow)">
      <animate attributeName="stroke-dasharray" values="0,600;300,300;600,0;300,300;0,600" dur="12s" begin="4s" repeatCount="indefinite"/>
    </path>
    <path d="M1520,350 Q1300,540 1600,800" stroke="url(#electricGrad)" stroke-width="1" fill="none" filter="url(#neonGlow)">
      <animate attributeName="stroke-dasharray" values="0,600;300,300;600,0;300,300;0,600" dur="14s" begin="1s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Holographic Interface Elements -->
  <g transform="translate(960,540)" opacity="0.3">
    <!-- Scanning Lines -->
    <g>
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="8s" repeatCount="indefinite"/>
      <line x1="0" y1="0" x2="200" y2="0" stroke="#00F5FF" stroke-width="1" opacity="0.8"/>
      <line x1="0" y1="0" x2="141" y2="141" stroke="#FF1744" stroke-width="1" opacity="0.8"/>
      <line x1="0" y1="0" x2="0" y2="200" stroke="#00E676" stroke-width="1" opacity="0.8"/>
      <line x1="0" y1="0" x2="-141" y2="141" stroke="#FF6D00" stroke-width="1" opacity="0.8"/>
    </g>
    
    <!-- Data Rings -->
    <circle r="250" fill="none" stroke="#00F5FF" stroke-width="1" opacity="0.5">
      <animate attributeName="r" values="200;300;200" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="stroke-dasharray" values="0,1570;785,785;1570,0;785,785;0,1570" dur="10s" repeatCount="indefinite"/>
    </circle>
    <circle r="320" fill="none" stroke="#FF1744" stroke-width="1" opacity="0.5">
      <animate attributeName="r" values="270;370;270" dur="12s" repeatCount="indefinite"/>
      <animate attributeName="stroke-dasharray" values="0,2010;1005,1005;2010,0;1005,1005;0,2010" dur="12s" begin="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Digital Rain with Glitch Effect -->
  <g opacity="0.2">
    <rect x="100" y="0" width="3" height="1080" fill="url(#electricGrad)">
      <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite" begin="0s"/>
      <animate attributeName="x" values="100;105;95;100" dur="0.1s" repeatCount="indefinite"/>
    </rect>
    <rect x="300" y="0" width="3" height="1080" fill="url(#electricGrad)">
      <animate attributeName="opacity" values="0;1;0" dur="5s" repeatCount="indefinite" begin="1s"/>
      <animate attributeName="x" values="300;302;298;300" dur="0.15s" repeatCount="indefinite"/>
    </rect>
    <rect x="700" y="0" width="3" height="1080" fill="url(#electricGrad)">
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite" begin="2s"/>
      <animate attributeName="x" values="700;703;697;700" dur="0.12s" repeatCount="indefinite"/>
    </rect>
    <rect x="1200" y="0" width="3" height="1080" fill="url(#electricGrad)">
      <animate attributeName="opacity" values="0;1;0" dur="6s" repeatCount="indefinite" begin="0.5s"/>
      <animate attributeName="x" values="1200;1198;1202;1200" dur="0.18s" repeatCount="indefinite"/>
    </rect>
    <rect x="1600" y="0" width="3" height="1080" fill="url(#electricGrad)">
      <animate attributeName="opacity" values="0;1;0" dur="4.5s" repeatCount="indefinite" begin="1.5s"/>
      <animate attributeName="x" values="1600;1604;1596;1600" dur="0.08s" repeatCount="indefinite"/>
    </rect>
  </g>
</svg>
