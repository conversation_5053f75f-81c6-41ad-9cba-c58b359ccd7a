<svg width="160" height="48" viewBox="0 0 160 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="160" height="48" rx="12" fill="url(#g)"/>
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif" font-size="18" font-weight="700" opacity="0.9">NEON</text>
  <defs>
    <linearGradient id="g" x1="0" y1="0" x2="160" y2="48">
      <stop offset="0" stop-color="#06b6d4"/>
      <stop offset="1" stop-color="#f472b6"/>
    </linearGradient>
  </defs>
</svg>
