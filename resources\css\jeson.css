@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Long";
  src: url("/fonts/long.woff");
}

@font-face {
  font-family: "Round";
  src: url("/fonts/round.woff");
}

@font-face {
  font-family: "Round Bold";
  font-weight: bold;
  src: url("/fonts/round-bold.woff");
}

@theme {
  --font-sans: "Round", sans-serif;
  --font-long: "Long", sans-serif;
  --font-round-bold: "Round Bold", sans-serif;
  --color-yellow: #fff9cb;
  --color-pink: #ffb0c4;
  --breakpoint-sm: 40rem;
  --breakpoint-xs: 20rem;
  --breakpoint-3xl: 120rem;
}

body {
  width: 100dvw;
  overflow-x: hidden;
  font-family: "Round", sans-serif;
  background-color: black;
}

main {
  background: linear-gradient(
    223.17deg,
    rgb(28, 24, 41) 0%,
    rgb(27, 24, 40) 8.61%,
    rgb(25, 23, 36) 17.21%,
    rgb(22, 21, 32) 25.82%,
    rgb(20, 19, 28) 34.42%,
    rgb(18, 18, 24) 43.03%,
    rgb(17, 17, 23) 51.63%
  );
}

@layer utilities {
  .text-yellow {
    color: #fff9cb;
  }
  .text-pink {
    color: #ffb0c4;
  }
  .bg-yellow {
    background-color: #fff9cb;
  }
  .font-long {
    font-family: "Long", sans-serif;
  }
  .font-round-bold {
    font-family: "Round Bold", sans-serif;
  }
  .flex-center {
    @apply flex justify-center items-center;
  }
  .col-center {
    @apply flex flex-col justify-center items-center;
  }
  .abs-center {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
  }
}

@layer components {
  main {
    @apply w-dvw overflow-x-hidden;
  }
  nav {
    @apply fixed top-0 left-0 w-full flex justify-between items-center md:p-16 p-5 z-[100];
  }
  .gradient-title {
    @apply md:text-[8rem] text-[5rem] font-round-bold !font-extrabold uppercase md:leading-[7rem] leading-[4.5rem] text-center bg-gradient-to-r from-pink-400 via-red-400 to-orange-400 bg-clip-text text-transparent;
  }
  .hero-section {
    @apply w-dvw h-dvh relative overflow-hidden;

    .scale-out {
      @apply object-cover md:scale-125 h-full;
    }
    .title-logo {
      @apply absolute h-full md:scale-125 top-0 object-cover;
    }
    .trailer-logo {
      @apply absolute -bottom-5 w-48 left-1/2 -translate-x-1/2;
    }
    .play-img {
      @apply rounded-full md:size-28 size-20 bg-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex justify-center items-center;
    }
    .fake-logo-wrapper {
      @apply absolute z-50 xs:top-[9.5rem] sm:top-[12.8rem] md:top-[8.5rem] 2xl:top-44 3xl:top-48 left-1/2 -translate-x-1/2;
    }
    .overlay-logo {
      @apply size-full object-cover opacity-0 w-60 md:w-60 2xl:w-72 3xl:w-80;
    }
  }
  .first-vd-wrapper {
    .first-vd {
      @apply size-full object-cover md:[object-position:50%_center] [object-position:75%_center];
    }
  }
  .entrance-message {
    @apply absolute inset-0 w-full h-dvh overflow-hidden z-20 opacity-100;

    .entrance-logo {
      @apply 2xl:w-72 3xl:w-80 md:w-60 w-48 absolute xs:top-[9.5rem] sm:top-[12.8rem] md:top-[8.5rem] 2xl:top-44 3xl:top-48 left-1/2 -translate-x-1/2;
    }

    .text-wrapper {
      @apply mt-40 md:mt-60 2xl:mt-44 3xl:top-48;
    }
  }
  .jason {
    @apply relative z-10 lg:ps-40 2xl:ps-80 ps-10 py-40 mt-60 flex lg:flex-row flex-col justify-between gap-5 w-dvw overflow-x-hidden;

    h1 {
      @apply text-yellow font-long uppercase text-8xl mb-20;
    }
    h2 {
      @apply text-pink md:text-5xl text-3xl mb-7 md:pe-20 pe-10;
    }
    p {
      @apply text-white md:text-2xl text-lg md:pe-28 pe-14;
    }
    .jason-1 {
      @apply bg-yellow lg:h-[80vh] w-auto -translate-x-5;

      img {
        @apply size-full object-cover [object-position:5%_center] hover:scale-[0.98] transition duration-700 ease-in-out;
      }
    }
    .jason-2 {
      @apply bg-yellow h-[90vh] w-auto md:mt-36 mt-20 -translate-x-5;

      img {
        @apply size-full object-cover [object-position:80%_center] hover:scale-x-[0.97] hover:scale-y-[0.98] transition duration-700 ease-in-out;
      }
    }
    .jason-3 {
      @apply bg-yellow h-[50vh] md:w-[60%] -translate-x-5;

      img {
        @apply size-full object-cover [object-position:42%_center] hover:scale-[0.97] transition duration-700 ease-in-out;
      }
    }
  }
  .lucia {
    @apply relative;
  }
  .lucia-life {
    @apply relative z-10 py-40 mt-60 flex lg:flex-row flex-col justify-between gap-5 w-dvw overflow-x-hidden;

    h1 {
      @apply text-yellow font-long uppercase text-8xl mb-20;
    }
    h2 {
      @apply text-pink md:text-5xl text-3xl mb-7 md:pe-20 pe-10;
    }
    p {
      @apply text-white md:text-2xl text-lg md:pe-28 pe-14;
    }
    p:last-of-type {
      @apply md:hidden block xl:block;
    }
    .lucia-1 {
      @apply bg-yellow lg:h-[80vh] w-auto -translate-x-5;

      img {
        @apply size-full object-cover [object-position:85%_center] hover:scale-[0.98] transition duration-700 ease-in-out;
      }
    }
    .lucia-2 {
      @apply bg-yellow lg:h-[60vh] lg:w-[70%] w-[80%] md:my-36 my-20 lg:-translate-x-5 translate-x-5;

      img {
        @apply size-full object-cover [object-position:50%_center] hover:scale-x-[0.98] hover:scale-y-[0.98] transition duration-700 ease-in-out;
      }
    }
    .lucia-3 {
      @apply bg-yellow lg:h-[90vh] md:w-[60%] -translate-x-5;

      img {
        @apply size-full object-cover [object-position:62%_center] hover:scale-x-[0.98] hover:scale-y-[0.99] transition duration-700 ease-in-out;
      }
    }
  }
  .post-card {
    @apply relative flex justify-center items-center pb-80 shadow-2xl;

    .animated-gradient-bg {
      @apply absolute w-full md:h-[200vh] h-[220vh] left-0 xl:-bottom-1/2 md:-bottom-5/6;
    }

    .post-card-wrapper {
      @apply xl:mx-56 md:mx-12 mx-5 xl:h-[85vh] md:h-[40vh] h-[30vh] w-full flex justify-center items-center overflow-hidden relative;

      video {
        @apply w-full h-full 2xl:scale-x-105 object-cover;
      }

      img {
        @apply absolute z-10 w-full h-full;
      }

      button {
        @apply rounded-full bg-white absolute left-1/2 -translate-x-1/2 md:bottom-16 -bottom-28 md:w-fit w-2/3 px-5 md:px-7 py-4 text-lg;
      }
    }
  }
  .final {
    @apply relative h-dvh overflow-hidden;

    .final-content {
      @apply scale-110;
    }
  }

  .final-message {
    @apply absolute w-full h-dvh overflow-hidden z-50 opacity-100;
  }
}

.black-gradient-bg {
  background: linear-gradient(
    223.17deg,
    rgb(28, 24, 41) 0%,
    rgb(27, 24, 40) 8.61%,
    rgb(25, 23, 36) 17.21%,
    rgb(22, 21, 32) 25.82%,
    rgb(20, 19, 28) 34.42%,
    rgb(18, 18, 24) 43.03%,
    rgb(17, 17, 23) 51.63%
  );
}

.entrance-message {
  mask-image: radial-gradient(circle at 50% 100vh, black 0%, transparent 0%);
  mask-repeat: no-repeat;
  mask-size: 100% 100%;
}

.mask-wrapper {
  mask-image: url("/images/big-hero-text.svg");
  mask-repeat: no-repeat;
}

.animated-gradient-bg {
  background: linear-gradient(135deg, #1e2a52 0%, #6e4b91 100%);
}

.jason-content {
  background: radial-gradient(ellipse, #111117 20%, transparent 70%);
}

.lucia-life-content {
  background: radial-gradient(ellipse, #111117 20%, transparent 70%);
}

.black-gradient-bg {
  background: linear-gradient(
    223.17deg,
    rgb(28, 24, 41) 0%,
    rgb(27, 24, 40) 8.61%,
    rgb(25, 23, 36) 17.21%,
    rgb(22, 21, 32) 25.82%,
    rgb(20, 19, 28) 34.42%,
    rgb(18, 18, 24) 43.03%,
    rgb(17, 17, 23) 51.63%
  );
}

.entrance-message {
  mask-image: radial-gradient(circle at 50% 100vh, black 0%, transparent 0%);
  mask-repeat: no-repeat;
  mask-size: 100% 100%;
}

.mask-wrapper {
  mask-image: url("/images/big-hero-text.svg");
  mask-repeat: no-repeat;
}

.animated-gradient-bg {
  background: linear-gradient(135deg, #1e2a52 0%, #6e4b91 100%);
}

.jason-content {
  background: radial-gradient(ellipse, #111117 20%, transparent 70%);
}

.lucia-life-content {
  background: radial-gradient(ellipse, #111117 20%, transparent 70%);
}