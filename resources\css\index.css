@import url("https://fonts.cdnfonts.com/css/general-sans");
@import url("https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @font-face {
    font-family: "circular-web";
    src: url("/fonts/circularweb-book.woff2") format("woff2");
  }

  @font-face {
    font-family: "general";
    src: url("/fonts/general.woff2") format("woff2");
  }

  @font-face {
    font-family: "robert-medium";
    src: url("/fonts/robert-medium.woff2") format("woff2");
  }

  @font-face {
    font-family: "robert-regular";
    src: url("/fonts/robert-regular.woff2") format("woff2");
  }

  @font-face {
    font-family: "zentry";
    src: url("/fonts/zentry-regular.woff2") format("woff2");
  }

  html, body {
    margin: 0;
    padding: 0;
    /* Use 100% to avoid mobile viewport-width bugs that include scrollbars */
    width: 100%;
    overflow-x: hidden;
    font-family: "General Sans", sans-serif;
    background-color: #dfdff0;
  }

  body.menu-open {
    background-color: transparent !important;
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}

@layer utilities {
  .border-hsla {
    @apply border border-white/20;
  }

  .nav-hover-btn {
    @apply relative ms-10 font-general text-xs uppercase text-blue-50 cursor-pointer;
  }
  
  .nav-hover-btn::after {
    content: '';
    position: absolute;
    bottom: -0.125rem;
    left: 0;
    height: 2px;
    width: 100%;
    background-color: rgb(38 38 38);
    transform-origin: bottom right;
    transform: scaleX(0);
    transition: transform 300ms cubic-bezier(0.65, 0.05, 0.36, 1);
  }
  
  .nav-hover-btn:hover::after {
    transform-origin: bottom left;
    transform: scaleX(1);
  }
  
  .dark .nav-hover-btn::after {
    background-color: white;
  }

  .floating-nav {
    @apply bg-black rounded-lg border;
  }

  .absolute-center {
    @apply absolute top-1/2 left-1/2 translate-x-[-50%] translate-y-[-50%];
  }

  .flex-center {
    @apply flex justify-center items-center;
  }

  .mask-clip-path {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }

  .special-font b {
    font-family: "Zentry";
    font-feature-settings: "ss01" on;
  }

  .hero-heading {
    @apply uppercase font-zentry font-black text-5xl sm:right-10 sm:text-7xl md:text-9xl lg:text-[12rem];
  }

  .about-subtext {
    @apply absolute bottom-[-80dvh] left-1/2 w-full max-w-96 -translate-x-1/2 text-center font-circular-web text-lg md:max-w-[34rem];
  }

  .about-image {
    @apply absolute left-1/2 top-0 z-20 h-[60vh] w-96 origin-center -translate-x-1/2 overflow-hidden rounded-3xl md:w-[30vw];
  }

  .animated-title {
    @apply flex flex-col gap-1 text-7xl uppercase leading-[.8] text-white sm:px-32 md:text-[6rem];
  }

  .animated-word {
    @apply special-font font-zentry font-black opacity-0;
    transform: translate3d(10px, 51px, -60px) rotateY(60deg) rotateX(-40deg);
    transform-origin: 50% 50% -150px;
    will-change: opacity, transform;
  }

  .bento-tilt_1 {
    @apply relative border-hsla col-span-2 overflow-hidden rounded-md transition-transform duration-300 ease-out;
  }

  .bento-tilt_2 {
    @apply relative col-span-1 row-span-1 overflow-hidden rounded-md transition-transform duration-300 ease-out;
  }

  .bento-title {
    @apply uppercase md:text-6xl text-4xl font-black font-zentry;
  }

  .story-img-container {
    @apply relative md:h-dvh h-[90vh] w-full;
    filter: url("#flt_tag");
  }

  .story-img-mask {
    @apply absolute left-0 top-0 size-full overflow-hidden md:left-[20%] md:top-[-10%] md:size-4/5;
    clip-path: polygon(4% 0, 83% 21%, 100% 73%, 0% 100%);
  }

  .story-img-content {
    @apply absolute w-full md:h-dvh h-[50dvh] opacity-100 left-10 top-16 md:left-0 md:top-10 lg:left-[-300px] lg:top-[-100px];
    transform: translate3d(0, 0, 0) rotateX(0) rotateY(0) rotateZ(0) scale(1);
  }

  .gallery-img-container {
    @apply size-64 overflow-hidden bg-violet-300;
  }

  .gallery-img {
    @apply size-full bg-cover;
  }

  .gallery-img-4 {
    @apply sm:size-80 md:h-96 md:w-[25rem] rounded-lg;
  }

  .sword-man-clip-path {
    clip-path: polygon(16% 0, 89% 15%, 75% 100%, 0 97%);
  }

  .contact-clip-path-1 {
    clip-path: polygon(25% 0%, 74% 0, 69% 64%, 34% 73%);
  }

  .contact-clip-path-2 {
    clip-path: polygon(29% 15%, 85% 30%, 50% 100%, 10% 64%);
  }

  .hamburger-line {
    transform-origin: center;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), background-color 0.3s ease;
    z-index: 100002;
    background-color: white;
  }

  .hamburger-open .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5.5px, 5.5px);
    background-color: black;
    z-index: 100002;
  }

  .hamburger-open .hamburger-line:nth-child(2) {
    opacity: 0;
    background-color: black;
    z-index: 100002;
  }

  .hamburger-open .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(5.5px, -5.5px);
    background-color: black;
    z-index: 100002;
  }

  .mobile-menu-overlay {
    font-family: 'Bebas Neue', sans-serif;
    pointer-events: all;
    touch-action: none;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  width: 100%;
  height: 100vh;
    margin: 0;
    padding: 0;
    z-index: 9998;
  }

  .mobile-menu-item {
    font-family: 'Bebas Neue', sans-serif;
    letter-spacing: -0.02em;
    transition: all 0.3s ease;
    pointer-events: all;
    display: block;
    text-decoration: none;
    z-index: 9999;
  }

  .mobile-menu-item:hover {
    transform: scale(1.02);
    color: #374151;
  }
}

.indicator-line {
  @apply h-3 w-px rounded-full bg-white transition-all duration-200 ease-in-out;
}

.indicator-line.active {
  animation: indicator-line 0.5s ease infinite;
  animation-delay: calc(var(--animation-order) * 0.1s);
}

@keyframes indicator-line {
  0% {
    height: 4px;
    transform: translateY(-0px);
  }
  50% {
    height: 16px;
    transform: translateY(-4px);
  }
  100% {
    height: 4px;
    transform: translateY(-0px);
  }
}

/* From Uiverse.io by G4b413l */
.three-body {
  --uib-size: 35px;
  --uib-speed: 0.8s;
  --uib-color: #5d3fd3;
  position: relative;
  display: inline-block;
  height: var(--uib-size);
  width: var(--uib-size);
  animation: spin78236 calc(var(--uib-speed) * 2.5) infinite linear;
}

.three-body__dot {
  position: absolute;
  height: 100%;
  width: 30%;
}

.three-body__dot:after {
  content: "";
  position: absolute;
  height: 0%;
  width: 100%;
  padding-bottom: 100%;
  background-color: var(--uib-color);
  border-radius: 50%;
}

.three-body__dot:nth-child(1) {
  bottom: 5%;
  left: 0;
  transform: rotate(60deg);
  transform-origin: 50% 85%;
}

.three-body__dot:nth-child(1)::after {
  bottom: 0;
  left: 0;
  animation: wobble1 var(--uib-speed) infinite ease-in-out;
  animation-delay: calc(var(--uib-speed) * -0.3);
}

.three-body__dot:nth-child(2) {
  bottom: 5%;
  right: 0;
  transform: rotate(-60deg);
  transform-origin: 50% 85%;
}

.three-body__dot:nth-child(2)::after {
  bottom: 0;
  left: 0;
  animation: wobble1 var(--uib-speed) infinite calc(var(--uib-speed) * -0.15)
    ease-in-out;
}

.three-body__dot:nth-child(3) {
  bottom: -5%;
  left: 0;
  transform: translateX(116.666%);
}

.three-body__dot:nth-child(3)::after {
  top: 0;
  left: 0;
  animation: wobble2 var(--uib-speed) infinite ease-in-out;
}

@keyframes spin78236 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes wobble1 {
  0%,
  100% {
    transform: translateY(0%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-66%) scale(0.65);
    opacity: 0.8;
  }
}

@keyframes wobble2 {
  0%,
  100% {
    transform: translateY(0%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(66%) scale(0.65);
    opacity: 0.8;
  }
}

/* Ensure smooth animations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


/* Brand Slider Styles */
.brand-slider-section {
  perspective: 1000px;
  transform-style: preserve-3d;
  will-change: transform;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .brand-slider-section {
    perspective: 500px;
  }

  .brand-item {
    transform: translateZ(0); /* Force hardware acceleration */
  }
}

.brand-item {
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.brand-glow {
  animation: brandGlow 4s ease-in-out infinite alternate;
}

@keyframes brandGlow {
  0% {
    opacity: 0.3;
    transform: scale(0.95);
  }
  100% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.brand-bg-element {
  animation: float 6s ease-in-out infinite;
}

@keyframes brandFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Enhanced scrollbar for horizontal sections */
.brand-slider-section::-webkit-scrollbar {
  display: none;
}

/* Smooth scroll behavior */
.brand-slider-section {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 3D hover effects */
.brand-item:hover {
  transform-style: preserve-3d;
}

/* Gradient text animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.brand-slider-section h2 {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* reduced motion fallback */
@media (prefers-reduced-motion: reduce) {
  * {
    scroll-behavior: auto !important;
    animation: none !important;
    transition: none !important;
  }

  .brand-glow {
    animation: none;
  }

  .brand-bg-element {
    animation: none;
  }
}

/* oil-paint filter suggestion for portraits */
.oil-paint {
  filter: contrast(0.95) saturate(0.9) sepia(0.12) blur(0.2px);
  /* for heavier effect you'd use a canvas post-process */
}
